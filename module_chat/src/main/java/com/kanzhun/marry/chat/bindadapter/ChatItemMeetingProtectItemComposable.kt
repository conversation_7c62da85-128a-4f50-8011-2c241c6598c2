package com.kanzhun.marry.chat.bindadapter

import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.fragment.app.FragmentActivity
import coil.compose.AsyncImage
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.kotlin.ext.painterResource
import com.kanzhun.common.kotlin.ext.toShareTime
import com.kanzhun.common.util.LDate
import com.kanzhun.foundation.bean.MapNavigationLocation
import com.kanzhun.foundation.dialog.ComposeDialogFragment
import com.kanzhun.foundation.kotlin.ktx.simplePost
import com.kanzhun.foundation.map.MapNavigationBottomDialog
import com.kanzhun.foundation.model.message.MeetingProtectCardItemSetting
import com.kanzhun.foundation.permission.PermissionData
import com.kanzhun.foundation.permission.PermissionHelper
import com.kanzhun.foundation.router.ChatPageRouter
import com.kanzhun.foundation.router.ChatPageRouterKT.jumpToMeetingLocationActivity
import com.kanzhun.foundation.utils.CalendarCommon
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.chat.R
import com.kanzhun.marry.chat.api.ChatApi
import com.kanzhun.marry.chat.model.MeetingConflictResponse
import com.kanzhun.utils.GsonUtils
import com.kanzhun.utils.T
import com.petterp.floatingx.imp.FxAppLifecycleProvider


@Preview(widthDp = 257)
@Composable
fun PreviewMeetingProtectCardItemSettingCard0() {
    bindMeetingProtectCardItemSettingCard(
        userId = "1",
        meetingProtectCardItemSetting = MeetingProtectCardItemSetting(
            status = 10,
            settingActiveId = "",
            inviteActiveId = "",
            cancelUserId = "",
            time = 0L,
            address = "见面地点",
            ""
        )
    )
}

@Preview(widthDp = 257)
@Composable
fun PreviewMeetingProtectCardItemSettingCard1() {
    bindMeetingProtectCardItemSettingCard(
        userId = "1",
        meetingProtectCardItemSetting = MeetingProtectCardItemSetting(
            status = 10,
            settingActiveId = "",
            inviteActiveId = "1",
            cancelUserId = "",
            time = 0L,
            address = "见面地点",
            ""
        )
    )
}

@Preview(widthDp = 257)
@Composable
fun PreviewMeetingProtectCardItemSettingCard2() {
    bindMeetingProtectCardItemSettingCard(
        userId = "2",
        meetingProtectCardItemSetting = MeetingProtectCardItemSetting(
            status = 10,
            settingActiveId = "",
            inviteActiveId = "1",
            cancelUserId = "",
            time = 0L,
            address = "见面地点",
            ""
        )
    )
}

@Preview(widthDp = 257)
@Composable
fun PreviewMeetingProtectCardItemSettingCard3() {
    bindMeetingProtectCardItemSettingCard(
        userId = "1",
        meetingProtectCardItemSetting = MeetingProtectCardItemSetting(
            status = 11,
            settingActiveId = "1",
            inviteActiveId = "",
            cancelUserId = "",
            time = 0L,
            address = "见面地点",
            ""
        )
    )
}

@Preview(widthDp = 257)
@Composable
fun PreviewMeetingProtectCardItemSettingCard4() {
    bindMeetingProtectCardItemSettingCard(
        userId = "1",
        meetingProtectCardItemSetting = MeetingProtectCardItemSetting(
            status = 11,
            settingActiveId = "2",
            inviteActiveId = "",
            cancelUserId = "",
            time = 0L,
            address = "见面地点",
            ""
        )
    )
}

@Preview(widthDp = 257)
@Composable
fun PreviewMeetingProtectCardItemSettingCard5() {
    bindMeetingProtectCardItemSettingCard(
        userId = "1",
        meetingProtectCardItemSetting = MeetingProtectCardItemSetting(
            status = 12,
            settingActiveId = "1",
            inviteActiveId = "",
            cancelUserId = "",
            time = 0L,
            address = "见面地点",
            ""
        )
    )
}

@Preview(widthDp = 257)
@Composable
fun PreviewMeetingProtectCardItemSettingCard6() {
    bindMeetingProtectCardItemSettingCard(
        userId = "2",
        meetingProtectCardItemSetting = MeetingProtectCardItemSetting(
            status = 12,
            settingActiveId = "1",
            inviteActiveId = "",
            cancelUserId = "",
            time = 0L,
            address = "见面地点",
            ""
        )
    )
}

@Preview(widthDp = 257)
@Composable
fun PreviewMeetingProtectCardItemSettingCard7() {
    bindMeetingProtectCardItemSettingCard(
        userId = "1",
        meetingProtectCardItemSetting = MeetingProtectCardItemSetting(
            status = 13,
            settingActiveId = "",
            inviteActiveId = "",
            cancelUserId = "1",
            time = 0L,
            address = "见面地点",
            ""
        )
    )
}


@Preview(widthDp = 257)
@Composable
fun PreviewMeetingProtectCardItemSettingCard8() {
    bindMeetingProtectCardItemSettingCard(
        userId = "2",
        meetingProtectCardItemSetting = MeetingProtectCardItemSetting(
            status = 13,
            settingActiveId = "",
            inviteActiveId = "",
            cancelUserId = "1",
            time = 0L,
            address = "见面地点",
            ""
        )
    )
}

@Composable
fun bindMeetingProtectCardItemSettingCard(
    meetingProtectCardItemSetting: MeetingProtectCardItemSetting,
    userId: String,
    peerId: String = "",
    recordId: String = "",
    msgId: String = "",
    userName: String = "",
) {
    var style = 0//0 双按钮  1 等待对方设置文案  2 单按钮  3取消  4时间+地点+双按钮  5 时间+地点+单按钮
    var bottomStr = ""
    var btnTxt1 = ""
    var btnTxt2 = ""
    var onClick1: () -> Unit = {}
    var onClick2: () -> Unit = {}
    var cardClick:() -> Unit = {}
    var mapIconClick:(address: String) -> Unit = {}
    val context = LocalContext.current

    var slon: Double = 0.0//经度
    var slat: Double = 0.0//纬度

    if (meetingProtectCardItemSetting.gps?.isNotEmpty() == true && meetingProtectCardItemSetting.gps?.contains(",") == true){
        slon = (meetingProtectCardItemSetting.gps?.substringBefore(",")?:"0").toDouble()
        slat = (meetingProtectCardItemSetting.gps?.substringAfter(",")?:"0").toDouble()
    }


    when (meetingProtectCardItemSetting.status) {
        10 -> {//设置状态 10- 未设置
            if (meetingProtectCardItemSetting.inviteActiveId?.isNotEmpty() == true) {
                if (meetingProtectCardItemSetting.inviteActiveId.equals(userId)) {
                    style = 1
                    bottomStr = "等待对方设置"
                } else {
                    style = 2
                    btnTxt1 = "去设置"
                    onClick1 = {
                        gotoSet(context = context, recordId = recordId,msgId = msgId)
                        reportPoint("O2-seeyou-positionsetcard-click"){
                            actionp2 = meetingProtectCardItemSetting.status.toString()
                            actionp3 = GsonUtils.toJson(meetingProtectCardItemSetting)
                            actionp4 = "去设置"
                            peer_id = peerId
                        }
                    }
                }
            } else {
                if ((meetingProtectCardItemSetting.settingActiveId?:"").isEmpty()){
                    //没人设置
                    style = 0
                    btnTxt1 = "邀Ta设置"
                    btnTxt2 = "去设置"
                    onClick1 = {
                        inviteOther(recordId = recordId, msgId = msgId)
                        reportPoint("O2-seeyou-positionsetcard-click"){
                            actionp2 = meetingProtectCardItemSetting.status.toString()
                            actionp3 = GsonUtils.toJson(meetingProtectCardItemSetting)
                            actionp4 = "邀Ta设置"
                            peer_id = peerId
                        }
                    }
                    onClick2 = {
                        gotoSet(context = context, recordId = recordId,msgId = msgId)
                        reportPoint("O2-seeyou-positionsetcard-click"){
                            actionp2 = meetingProtectCardItemSetting.status.toString()
                            actionp3 = GsonUtils.toJson(meetingProtectCardItemSetting)
                            actionp4 = "去设置"
                            peer_id = peerId
                        }
                    }
                }else if(meetingProtectCardItemSetting.settingActiveId?.equals(userId) == true){
                    //我设置
                    style = 0
                    btnTxt1 = "邀Ta设置"
                    btnTxt2 = "去设置"
                    onClick1 = {
                        inviteOther(recordId = recordId, msgId = msgId)
                        reportPoint("O2-seeyou-positionsetcard-click"){
                            actionp2 = meetingProtectCardItemSetting.status.toString()
                            actionp3 = GsonUtils.toJson(meetingProtectCardItemSetting)
                            actionp4 = "邀Ta设置"
                            peer_id = peerId
                        }
                    }
                    onClick2 = {
                        gotoSet(context = context, recordId = recordId,msgId = msgId)
                        reportPoint("O2-seeyou-positionsetcard-click"){
                            actionp2 = meetingProtectCardItemSetting.status.toString()
                            actionp3 = GsonUtils.toJson(meetingProtectCardItemSetting)
                            actionp4 = "去设置"
                            peer_id = peerId
                        }
                    }
                }else{
                    style = 1
                    bottomStr = "等待对方设置见面信息"
                }

            }
        }

        11 -> {//设置状态 11- 已设置
            if (meetingProtectCardItemSetting.settingActiveId.equals(userId)) {
                //我是主动设置放  等待对方设置
                style = 6
                bottomStr = "已发送见面计划，等待对方回应…"
            } else {
                //如果对方已经设置
                style = 4
                btnTxt1 = "修改一下"
                btnTxt2 = "我同意"
                onClick1 = {
                    gotoSet(context = context, recordId = recordId,msgId = msgId, doNet = false)
                    reportPoint("O2-seeyou-positionsetcard-click"){
                        actionp2 = meetingProtectCardItemSetting.status.toString()
                        actionp3 = GsonUtils.toJson(meetingProtectCardItemSetting)
                        actionp4 = "修改一下"
                        peer_id = peerId
                    }
                }
                onClick2 = {
                    checkMeeting(recordId = recordId, msgId = msgId,peerId= peerId)
                    reportPoint("O2-seeyou-positionsetcard-click"){
                        actionp2 = meetingProtectCardItemSetting.status.toString()
                        actionp3 = GsonUtils.toJson(meetingProtectCardItemSetting)
                        actionp4 = "我同意"
                        peer_id = peerId
                    }
                }
            }
            cardClick = {
                if (FxAppLifecycleProvider.getTopActivity() != null){
                    jumpToMeetingLocationActivity(FxAppLifecycleProvider.getTopActivity()!!, recordId, PageSource.NONE)
                }
                reportPoint("O2-seeyou-positionsetcard-click"){
                    actionp2 = meetingProtectCardItemSetting.status.toString()
                    actionp3 = GsonUtils.toJson(meetingProtectCardItemSetting)
                    actionp4 = "卡片"
                    peer_id = peerId
                }
            }
            mapIconClick = { address ->
                if (FxAppLifecycleProvider.getTopActivity() is FragmentActivity) {
                    val navigationLocation =
                        MapNavigationLocation(slat = slat, slon = slon, address, null)
                    MapNavigationBottomDialog(context = FxAppLifecycleProvider.getTopActivity() as FragmentActivity).show(
                        startLocation = null,
                        targetLocation = navigationLocation,
                        0
                    )
                }
                reportPoint("O2-seeyou-positionsetcard-click"){
                    actionp2 = meetingProtectCardItemSetting.status.toString()
                    actionp3 = GsonUtils.toJson(meetingProtectCardItemSetting)
                    actionp4 = "导航"
                    peer_id = peerId
                }
            }
        }

        12 -> {//设置状态 12- 已同意
            style = 5
            btnTxt1 = "添加日历"
            onClick1 = {
                setCalendar(
                    context,
                    meetingProtectCardItemSetting.time,
                    meetingProtectCardItemSetting.time,
                    name =  userName,
                )
                reportPoint("O2-seeyou-positionsetcard-click"){
                    actionp2 = meetingProtectCardItemSetting.status.toString()
                    actionp3 = GsonUtils.toJson(meetingProtectCardItemSetting)
                    actionp4 = "添加日历"
                    peer_id = peerId
                }
            }
            cardClick = {
                if (FxAppLifecycleProvider.getTopActivity() != null){
                    jumpToMeetingLocationActivity(FxAppLifecycleProvider.getTopActivity()!!, recordId, PageSource.NONE)
                }
                reportPoint("O2-seeyou-positionsetcard-click"){
                    actionp2 = meetingProtectCardItemSetting.status.toString()
                    actionp3 = GsonUtils.toJson(meetingProtectCardItemSetting)
                    actionp4 = "卡片"
                    peer_id = peerId
                }
            }
            mapIconClick = { address ->
                if (FxAppLifecycleProvider.getTopActivity() is FragmentActivity) {
                    val navigationLocation =
                        MapNavigationLocation(slat = slat, slon = slon, address, null)
                    MapNavigationBottomDialog(context = FxAppLifecycleProvider.getTopActivity() as FragmentActivity).show(
                        startLocation = null,
                        targetLocation = navigationLocation,
                        0
                    )
                }
                reportPoint("O2-seeyou-positionsetcard-click"){
                    actionp2 = meetingProtectCardItemSetting.status.toString()
                    actionp3 = GsonUtils.toJson(meetingProtectCardItemSetting)
                    actionp4 = "导航"
                    peer_id = peerId
                }
            }
        }

        13 -> {//设置状态 13- 已取消
            style = 3
            bottomStr = if (meetingProtectCardItemSetting.cancelUserId.equals(userId)) {
                "您已取消见面计划"
            } else {
                "对方已取消见面计划"
            }
            cardClick = {
                if (FxAppLifecycleProvider.getTopActivity() != null){
                    jumpToMeetingLocationActivity(FxAppLifecycleProvider.getTopActivity()!!, recordId, PageSource.NONE)
                }
                reportPoint("O2-seeyou-positionsetcard-click"){
                    actionp2 = meetingProtectCardItemSetting.status.toString()
                    actionp3 = GsonUtils.toJson(meetingProtectCardItemSetting)
                    actionp4 = "卡片"
                    peer_id = peerId
                }
            }
            mapIconClick = { address ->
                if (FxAppLifecycleProvider.getTopActivity() is FragmentActivity) {
                    val navigationLocation =
                        MapNavigationLocation(slat = slat, slon = slon, address, null)
                    MapNavigationBottomDialog(context = FxAppLifecycleProvider.getTopActivity() as FragmentActivity).show(
                        startLocation = null,
                        targetLocation = navigationLocation,
                        0
                    )
                }
                reportPoint("O2-seeyou-positionsetcard-click"){
                    actionp2 = meetingProtectCardItemSetting.status.toString()
                    actionp3 = GsonUtils.toJson(meetingProtectCardItemSetting)
                    actionp4 = "导航"
                    peer_id = peerId
                }
            }
        }
    }

    MeetingProtectCardItemSettingCard(
        style, meetingProtectCardItemSetting.time,
        meetingProtectCardItemSetting.address.toString(),
        bottomStr,
        onClick1,
        onClick2,
        btnTxt1,
        btnTxt2,
        cardClick,
        mapIconClick,
        slat = slat,
        slon = slon
    )

}

/**
 * data class MeetingProtectCardItemSetting(
 *     val status: Int,//设置状态 10- 未设置，11-已设置  12-已同意
 *     val settingActiveId: String,//主动设置uid
 *     val inviteActiveId: String,//主动邀请uid
 *     val time: Long,//时间，毫秒值
 *     val address: String,//见面地点
 * ): Serializable
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun MeetingProtectCardItemSettingCard(
    style: Int = 0,//0 双按钮  1 等待对方设置文案  2 单按钮  3取消  4时间+地点+双按钮  5 时间+地点+单按钮  6 时间+地点+灰条
    time: Long = 0,//时间，毫秒值
    address: String = "",//见面地点
    bottomStr: String = "",//取消原因
    onClick1: () -> Unit = {},
    onClick2: () -> Unit = {},
    btnTxt1: String = "取消",//取消按钮文案
    btnTxt2: String = "立即见面",//立即见面
    cardClick: () -> Unit = {},
    mapIconClick:(address:String)->Unit = {},
    slat: Double = 0.0,
    slon: Double = 0.0,
) {

    ConstraintLayout(
        modifier = Modifier
            .noRippleClickable {
                cardClick()
            }
            .fillMaxWidth()
    ) {
        val (imageBg, leftIcon, resText, resBtn) = createRefs()
        Image(
            painter = R.drawable.image_meeting_protect_item_setting_bg.painterResource(),
            contentDescription = "",
            contentScale = ContentScale.FillBounds,
            modifier = Modifier
                .constrainAs(imageBg) {
                    top.linkTo(leftIcon.top, 20.dp)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(resBtn.bottom)
                    height = Dimension.fillToConstraints
                })

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .constrainAs(leftIcon) {
                    top.linkTo(parent.top)
                    end.linkTo(parent.end, 12.dp)
                    start.linkTo(parent.start, 12.dp)
                    width = Dimension.fillToConstraints
                },
        ) {

            Image(
                painter = if (style == 3 ||  style == 5 ) R.drawable.image_meeting_protect_item_setting_title2.painterResource() else R.drawable.image_meeting_protect_item_setting_title.painterResource(),
                contentDescription = "",
                modifier = Modifier.weight(676.0f)
            )

            Column(modifier = Modifier.weight(480.0f)) {
                Spacer(modifier = Modifier.height(5.dp))
                Image(
                    painter = R.drawable.image_meeting_protect_item_setting_icon.painterResource(),
                    contentDescription = "",
                )

            }
        }

        Column(
            modifier = Modifier
                .constrainAs(resText) {
                    start.linkTo(parent.start, 24.dp)
                    top.linkTo(leftIcon.bottom, 12.dp)
                    end.linkTo(parent.end, 24.dp)
                    width = Dimension.fillToConstraints
                }) {
            if (style == 3 || style == 4 || style == 5 || style == 6) {
                Row {
                    Icon(
                        painter = R.drawable.icon_chat_item_meeting_protect_data.painterResource(),
                        contentDescription = "",
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = LDate.getMeetingData(time),
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF292929),
                        )
                    )
                }

                Spacer(modifier = Modifier.height(14.dp))

                Row(verticalAlignment = Alignment.CenterVertically) {
                    Icon(
                        painter = R.drawable.icon_chat_item_meeting_protect_location.painterResource(),
                        contentDescription = "",
                    )
                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = address,
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF292929),
                        ),
                        modifier = Modifier.weight(1.0f),
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Image(
                        painter = R.drawable.icon_chat_item_meeting_protect_map.painterResource(),
                        contentDescription = "",
                        modifier = Modifier
                            .width(27.dp)
                            .noRippleClickable {
                                mapIconClick(address)
                            }
                    )
                }


            } else {
                Text(
                    text = "来选择见面的时间和地点吧～",
                    style = TextStyle(
                        fontSize = 13.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF292929),
                    )
                )

                Spacer(modifier = Modifier.height(20.dp))

                Image(
                    painter = R.drawable.image_meeting_protect_item_setting_map.painterResource(),
                    contentDescription = "",
                )
            }

        }


        Column(modifier = Modifier.constrainAs(resBtn) {
            start.linkTo(parent.start, 24.dp)
            end.linkTo(parent.end, 24.dp)
            top.linkTo(resText.bottom, 20.dp)
            width = Dimension.fillToConstraints
        }) {
            if (style == 1 || style == 3 || style == 6) {
                //灰色文案
                Text(
                    text = bottomStr,
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(600),
                        color = Color(0xFFB8B8B8),
                        textAlign = TextAlign.Center,
                    ),
                    modifier = Modifier.fillMaxWidth()
                )
            } else if (style == 2) {
                //单按钮 -- 黑色
                Text(
                    text = btnTxt1,
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(500),
                        color = Color.White,
                        textAlign = TextAlign.Center,
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            color = Color(0xFF190E0C),
                            shape = RoundedCornerShape(25.dp)
                        )
                        .padding(vertical = 12.dp)
                        .noRippleClickable {
                            onClick1()
                        }
                )
            } else if (style == 5) {
                //单按钮 --蓝色
                Text(
                    text = btnTxt1,
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(700),
                        color = Color(0xFF0A4859),
                        textAlign = TextAlign.Center,
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            color = Color(0xFFC5EEF9),
                            shape = RoundedCornerShape(25.dp)
                        )
                        .padding(vertical = 12.dp)
                        .noRippleClickable {
                            onClick1()
                        }
                )
            } else if (style == 4 || style == 0) {
                //双按钮
                Row(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        text = btnTxt1,
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(700),
                            color = Color(0xFF0A4859),
                            textAlign = TextAlign.Center,
                        ),
                        modifier = Modifier
                            .weight(1f)
                            .background(
                                color = Color(0xFFC5EEF9),
                                shape = RoundedCornerShape(25.dp)
                            )
                            .padding(vertical = 12.dp)
                            .noRippleClickable {
                                onClick1()
                            }
                    )

                    Spacer(modifier = Modifier.width(10.dp))

                    Text(
                        text = btnTxt2,
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(500),
                            color = Color.White,
                            textAlign = TextAlign.Center,
                        ),
                        modifier = Modifier
                            .weight(1f)
                            .background(
                                color = Color(0xFF190E0C),
                                shape = RoundedCornerShape(25.dp)
                            )
                            .padding(vertical = 12.dp)
                            .noRippleClickable {
                                onClick2()
                            }
                    )
                }

            }

            Spacer(modifier = Modifier.height(24.dp))
        }


    }
}


//去设置
private fun gotoSet(context: Context, recordId: String, msgId: String,doNet:Boolean = true) {
    if (doNet){
        "orange/protect/meet/setting/goto".simplePost(
            mapOf("recordId" to recordId,"msgId" to msgId),
            autoErrorToast = true
        )
    }
    com.kanzhun.foundation.router.ChatPageRouterKT.jumpToMakeMeetingPlanActivity(
        context = context,
        recordId = recordId
    )
}

//添加日历
private fun setCalendar(context: Context, startTime: Long, endTime: Long,name: String = "") {
    PermissionHelper.getCalendarHelper(context as FragmentActivity)
        .setPermissionCallback { yes: Boolean, permission: PermissionData? ->
            if (yes) {
                val eventId = CalendarCommon(context).addEvent2(
                    "「见面提醒」",
                    "您与「${name}」在「${LDate.getMeetingData(startTime)}」有个约会日程，不要忘记哦～",
                    startTime,
                    endTime,
                    30
                )
                T.ss("已添加到日历")
            }
        }.requestPermission()

}

//确认
private fun goConfirm(recordId: String, msgId: String) {
    "orange/protect/meet/setting/accept".simplePost(
        mapOf("recordId" to recordId, "msgId" to msgId),
        autoErrorToast = true
    )
}

private fun checkMeeting(recordId: String, msgId: String,peerId: String) {
    val observable = RetrofitManager.getInstance().createApi<ChatApi>(ChatApi::class.java)
        .getProtectMeetupChatReviewDetail(recordId,"")
    HttpExecutor.execute<MeetingConflictResponse>(
        observable,
        object : BaseRequestCallback<MeetingConflictResponse?>(true) {
            override fun onSuccess(data: MeetingConflictResponse?) {
                if (data != null) {
                    //弹窗
                    showMeetingConflictDialog(data,peerId)
                } else {
                    //确认
                    goConfirm(recordId, msgId)
                }
            }

            override fun dealFail(reason: ErrorReason?) {

            }
        })
}

// This function will show a dialog when there's a meeting conflict
// The user mentioned they will implement the dialog UI
fun showMeetingConflictDialog(conflictResponse: MeetingConflictResponse,peerId: String) {
    if (FxAppLifecycleProvider.getTopActivity() is FragmentActivity) {
        val activity = FxAppLifecycleProvider.getTopActivity() as FragmentActivity
        // The dialog will be implemented by the user
        // Here we just prepare the data
        ComposeDialogFragment.shouldShow(
            activity = activity,
            canceledOnTouchOutside = true,
            cancelable = true
        ) { dialog ->
            MeetingConflictDialogContent(
                conflictResponse = conflictResponse,
                onItemClick = {
                    if (FxAppLifecycleProvider.getTopActivity() is FragmentActivity){
                        ChatPageRouter.jumpToSingleChatActivityForH5(FxAppLifecycleProvider.getTopActivity(), conflictResponse.userId)
                    }
                    dialog.dismiss()
                    reportPoint("O2-seeyou-timeconflict-click"){
                        actionp2 = "2"
                        actionp3 = conflictResponse.userId
                        actionp4 = it
                        peer_id = peerId
                    }
                },
                onDismiss = {
                    reportPoint("O2-seeyou-timeconflict-click"){
                        actionp2 = "1"
                        actionp4 = it
                        peer_id = peerId
                    }
                    dialog.dismiss()
                }
            )
        }
        reportPoint("O2-seeyou-timeconflictwin-show"){
            actionp2 = conflictResponse.userId
            actionp3 = "当前的见面时间"+LDate.getMeetingData(conflictResponse.time?:0)+"与您将要赴约的见面计划冲突，如您想取消之前的见面计划，可前往对应页面取消"
            peer_id = peerId
        }
    }
}

@Preview
@Composable
fun PreviewMeetingConflictDialogContent() {
    val conflictResponse = MeetingConflictResponse(
        userId = "",
        time = 0,
        tinyAvatar = "",
        nickName = "name",
        recommendTag = listOf("哈哈", "lulu","kaka","gogo","bobo","123456")
    )
    MeetingConflictDialogContent(conflictResponse, onItemClick = {

    }) {

    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun MeetingConflictDialogContent(
    conflictResponse: MeetingConflictResponse,
    onItemClick: (t: String) -> Unit,
    onDismiss: (t: String) -> Unit,
) {
    // This is a basic template for the conflict dialog
    // The user mentioned they will implement the full UI

    Box(modifier = Modifier
        .fillMaxSize()
        , contentAlignment = Alignment.Center){
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(32.dp)
                .background(Color.White, RoundedCornerShape(32.dp))
                .padding(24.dp)
        ) {
            // Title
            Text(
                text = "见面时间冲突",
                style = TextStyle(
                    fontSize = 20.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF292929),
                    textAlign = TextAlign.Center,
                ),
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(26.dp))

            // Conflict details - user can replace this with their image UI
            val t = buildAnnotatedString {
                append("当前的见面时间")
                withStyle(style = SpanStyle(color = Color(0xFF005EFF))) {
                    append(LDate.getMeetingData(conflictResponse.time?:0))
                }
                append("与您将要赴约的见面计划冲突，如您想取消之前的见面计划，可前往对应页面取消")
            }
            Text(
                text = t,
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Normal,
                    color = Color(0xFF292929)
                )
            )

            Spacer(modifier = Modifier.height(12.dp))

            Column(
                Modifier
                    .fillMaxWidth()
                    .background(Color(0xFFFFE9E1), RoundedCornerShape(16.dp))
                    .padding(2.dp)
                    .height(112.dp)
                    .noRippleClickable {
                        onItemClick(t.toString())
                    }
            ) {
                Row(
                    Modifier
                        .fillMaxWidth()
                        .height(76.dp)
                        .background(Color(0xFFFFFFFF), RoundedCornerShape(16.dp))
                        .padding(horizontal = 10.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AsyncImage(
                        model = conflictResponse.tinyAvatar ?: "",
                        contentDescription = "Profile Picture",
                        modifier = Modifier
                            .border(
                                width = 2.dp,
                                color = Color.White,
                                shape = CircleShape
                            )
                            .clip(CircleShape)
                            .size(56.dp)
                    )

                    Spacer(modifier = Modifier.width(10.dp))

                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text(
                                text = conflictResponse.nickName ?: "",
                                style = TextStyle(
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight(500),
                                    color = Color(0xFF190E0C),
                                ),
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis,
                                modifier = Modifier.weight(weight = 1f, fill = false)
                            )

                            Text(
                                text = "待见面",
                                style = TextStyle(
                                    fontSize = 10.sp,
                                    fontWeight = FontWeight(500),
                                    color = Color(0xFF7F7F7F),
                                ), modifier = Modifier
                                    .padding(start = 4.dp)
                                    .background(Color(0xFFF5F5F6), RoundedCornerShape(9.dp))
                                    .padding(vertical = 1.dp, horizontal = 5.dp)
                            )

                        }

                        Spacer(modifier = Modifier.height(6.dp))

                        FlowRow(
                            modifier = Modifier.fillMaxWidth(),
                            maxLines = 1,
                            horizontalArrangement = Arrangement.spacedBy(4.dp),
                            verticalArrangement = Arrangement.Center
                        ) {
                            conflictResponse.recommendTag?.forEach { tag ->
                                Box(modifier = Modifier.align(Alignment.CenterVertically)) {
                                    Text(
                                        text = tag,
                                        style = TextStyle(
                                            fontSize = 14.sp,
                                            fontWeight = FontWeight(400),
                                            color = Color(0xFF949494),
                                        )
                                    )
                                }
                            }
                        }
                    }

                    Icon(
                        painter = R.drawable.me_ic_right.painterResource(),
                        tint = Color(0xFF5E5E5E),
                        contentDescription = "Arrow Right",
                        modifier = Modifier
                            .size(14.dp)
                    )

                }

                Row(Modifier
                    .fillMaxWidth()
                    .height(36.dp)
                    .padding(horizontal = 10.dp), verticalAlignment = Alignment.CenterVertically) {
                    Image(
                        painter = R.drawable.icon_time.painterResource(),
                        contentDescription = "image description",
                        contentScale = ContentScale.FillBounds,
                        modifier = Modifier
                            .width(16.dp)
                            .height(16.dp)
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = LDate.getMeetingData(conflictResponse.time?:0),
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFFFF7847),
                        ),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                }
            }


            Spacer(modifier = Modifier.height(28.dp))

            // Button
            Text(
                text = "知道啦",
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.White,
                    textAlign = TextAlign.Center,
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        color = Color(0xFF190E0C),
                        shape = RoundedCornerShape(25.dp)
                    )
                    .padding(vertical = 12.dp)
                    .noRippleClickable {
                        onDismiss(t.toString())
                    }
            )
        }
    }

}

//邀请他人设置
private fun inviteOther(recordId: String, msgId: String) {
    "orange/protect/meet/setting/invite".simplePost(
        mapOf("recordId" to recordId, "msgId" to msgId), autoErrorToast = true
    )
}



