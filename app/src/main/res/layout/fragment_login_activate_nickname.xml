<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <com.kanzhun.foundation.views.CommonPageTitleView
        android:id="@+id/tv_phone_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title_all_page="@string/login_title_section"
        app:title_now_page="1"
        app:title_bottom_padding="@dimen/app_layout_page_content_margin"
        app:title_icon="@drawable/common_ic_icon_title_avatar"
        app:title_text="@string/login_activate_nick_name_title" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="28dp"
        android:layout_marginRight="28dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_phone_title">


        <EditText
            android:id="@+id/edit_text"
            android:layout_width="0dp"
            android:layout_height="33dp"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="@string/login_activate_nick_name_hint"
            android:singleLine="true"
            android:textColor="@color/common_color_191919"
            android:textColorHint="@color/common_color_CCCCCC"
            android:textCursorDrawable="@drawable/common_color_black_text_cursor"
            android:textSize="@dimen/common_text_sp_24"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <View
            android:id="@+id/viewEditLine"
            tools:background="@color/common_color_191919"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="12dp"
            android:visibility="visible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/edit_text" />

        <TextView
            android:visibility="gone"
            android:id="@+id/tv_error_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:textColor="@color/common_color_FF3F4B"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/viewEditLine" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.kanzhun.marry.me.views.LoginNextPageView
        android:id="@+id/btn_next"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="28dp"
        android:layout_marginBottom="28dp"
        android:enabled="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
